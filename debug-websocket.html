<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1f2937;
            color: white;
        }
        .log {
            background: #111827;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            height: 400px;
            overflow-y: auto;
            margin-top: 20px;
            white-space: pre-wrap;
        }
        button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #4f46e5; }
        input {
            padding: 8px;
            margin: 5px;
            border-radius: 4px;
            border: 1px solid #374151;
            background: #1f2937;
            color: white;
        }
    </style>
</head>
<body>
    <h1>WebSocket Debug Tool</h1>
    
    <div>
        <label>WebSocket URL:</label>
        <input type="text" id="wsUrl" value="ws://localhost:8765" style="width: 300px;">
        <button onclick="connect()">Connect</button>
        <button onclick="disconnect()">Disconnect</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <div>
        <label>Message:</label>
        <input type="text" id="message" value='{"type":"register","name":"Test User"}' style="width: 300px;">
        <button onclick="sendMessage()">Send</button>
    </div>
    
    <div class="log" id="log"></div>

    <script>
        let socket = null;
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('log');
            const logMessage = `[${timestamp}] ${message}\n`;
            logDiv.textContent += logMessage;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(logMessage.trim());
        }
        
        function connect() {
            const url = document.getElementById('wsUrl').value;
            
            if (socket) {
                log('Closing existing connection...');
                socket.close();
                socket = null;
            }
            
            log(`Attempting to connect to: ${url}`);
            
            try {
                socket = new WebSocket(url);
                
                socket.onopen = (event) => {
                    log('✅ WebSocket connection opened!');
                    log(`ReadyState: ${socket.readyState}`);
                    log(`Protocol: ${socket.protocol}`);
                    log(`URL: ${socket.url}`);
                };
                
                socket.onmessage = (event) => {
                    log(`📨 Received: ${event.data}`);
                };
                
                socket.onclose = (event) => {
                    log(`❌ Connection closed`);
                    log(`Code: ${event.code}`);
                    log(`Reason: ${event.reason}`);
                    log(`WasClean: ${event.wasClean}`);
                };
                
                socket.onerror = (event) => {
                    log(`❌ WebSocket error occurred`);
                    log(`Error event: ${JSON.stringify(event)}`);
                };
                
                // Log state changes
                const checkState = () => {
                    if (socket) {
                        const states = {
                            0: 'CONNECTING',
                            1: 'OPEN',
                            2: 'CLOSING',
                            3: 'CLOSED'
                        };
                        log(`State: ${states[socket.readyState]} (${socket.readyState})`);
                        
                        if (socket.readyState === WebSocket.CONNECTING) {
                            setTimeout(checkState, 1000);
                        }
                    }
                };
                
                setTimeout(checkState, 100);
                
            } catch (error) {
                log(`❌ Failed to create WebSocket: ${error.message}`);
                log(`Error stack: ${error.stack}`);
            }
        }
        
        function disconnect() {
            if (socket) {
                log('Disconnecting...');
                socket.close();
                socket = null;
            } else {
                log('No active connection to disconnect');
            }
        }
        
        function sendMessage() {
            const message = document.getElementById('message').value;
            
            if (!socket) {
                log('❌ No active connection');
                return;
            }
            
            if (socket.readyState !== WebSocket.OPEN) {
                log(`❌ Connection not open (state: ${socket.readyState})`);
                return;
            }
            
            try {
                socket.send(message);
                log(`📤 Sent: ${message}`);
            } catch (error) {
                log(`❌ Failed to send message: ${error.message}`);
            }
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
        }
        
        // Test browser WebSocket support
        window.addEventListener('load', () => {
            log('WebSocket Debug Tool loaded');
            log(`WebSocket support: ${!!window.WebSocket}`);
            log(`Current URL: ${window.location.href}`);
            log(`Protocol: ${window.location.protocol}`);
            log(`Hostname: ${window.location.hostname}`);
            log(`Port: ${window.location.port}`);
            log('Ready to test WebSocket connection');
        });
    </script>
</body>
</html>
