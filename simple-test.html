<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1f2937;
            color: white;
        }
        .status {
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            font-size: 18px;
            text-align: center;
        }
        .connecting { background: #f59e0b; }
        .connected { background: #10b981; }
        .disconnected { background: #ef4444; }
        .error { background: #dc2626; }
        button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        button:hover { background: #4f46e5; }
        .log {
            background: #111827;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
            height: 300px;
            overflow-y: auto;
            margin-top: 20px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Simple WebSocket Connection Test</h1>
    
    <div id="status" class="status disconnected">Not Connected</div>
    
    <div>
        <button onclick="testConnection()">Test Connection</button>
        <button onclick="createRoom()">Create Room</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <div class="log" id="log"></div>

    <script>
        let socket = null;
        
        function updateStatus(message, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${className}`;
        }
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('log');
            const logMessage = `[${timestamp}] ${message}\n`;
            logDiv.textContent += logMessage;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(logMessage.trim());
        }
        
        function testConnection() {
            log('Starting connection test...');
            updateStatus('Connecting...', 'connecting');
            
            if (socket) {
                socket.close();
                socket = null;
            }
            
            try {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const hostname = window.location.hostname;
                const wsUrl = `${protocol}//${hostname}:8765`;
                
                log(`Attempting to connect to: ${wsUrl}`);
                log(`Current page: ${window.location.href}`);
                
                socket = new WebSocket(wsUrl);
                
                socket.onopen = () => {
                    log('✅ WebSocket connection successful!');
                    updateStatus('Connected!', 'connected');
                    
                    // Register user
                    const userName = prompt('Enter your display name:') || 'Test User';
                    const registerMessage = {
                        type: 'register',
                        name: userName
                    };
                    
                    socket.send(JSON.stringify(registerMessage));
                    log(`Sent registration: ${JSON.stringify(registerMessage)}`);
                };
                
                socket.onmessage = (event) => {
                    log(`📨 Received: ${event.data}`);
                    
                    try {
                        const data = JSON.parse(event.data);
                        if (data.type === 'registered') {
                            log(`✅ Successfully registered with ID: ${data.user_id}`);
                        }
                    } catch (e) {
                        log(`Could not parse message as JSON: ${e.message}`);
                    }
                };
                
                socket.onclose = (event) => {
                    log(`❌ Connection closed. Code: ${event.code}, Reason: ${event.reason}`);
                    updateStatus('Disconnected', 'disconnected');
                };
                
                socket.onerror = (error) => {
                    log(`❌ WebSocket error: ${error}`);
                    log(`Error type: ${error.type}`);
                    updateStatus('Connection Error', 'error');
                };
                
                // Timeout check
                setTimeout(() => {
                    if (socket && socket.readyState === WebSocket.CONNECTING) {
                        log('⚠️ Still connecting after 5 seconds...');
                    }
                }, 5000);
                
            } catch (error) {
                log(`❌ Failed to create WebSocket: ${error.message}`);
                updateStatus('Connection Failed', 'error');
            }
        }
        
        function createRoom() {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                log('❌ Not connected. Please test connection first.');
                return;
            }
            
            const roomMessage = {
                type: 'create_room',
                room_name: 'Test Room',
                max_users: 10
            };
            
            socket.send(JSON.stringify(roomMessage));
            log(`Sent create room: ${JSON.stringify(roomMessage)}`);
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
        }
        
        // Auto-test on load
        window.addEventListener('load', () => {
            log('Page loaded. Ready to test WebSocket connection.');
            log(`Browser WebSocket support: ${!!window.WebSocket}`);
            log(`Current URL: ${window.location.href}`);
            
            // Auto-connect after a short delay
            setTimeout(() => {
                log('Auto-starting connection test...');
                testConnection();
            }, 1000);
        });
    </script>
</body>
</html>
