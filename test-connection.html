<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1f2937;
            color: white;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #10b981; }
        .error { background: #ef4444; }
        .info { background: #3b82f6; }
        .warning { background: #f59e0b; }
        button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #4f46e5; }
        #log {
            background: #111827;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    
    <div id="status" class="status info">Ready to test connection...</div>
    
    <button onclick="testConnection()">Test WebSocket Connection</button>
    <button onclick="testDisplayName()">Test Display Name Prompt</button>
    <button onclick="clearLog()">Clear Log</button>
    
    <div id="log"></div>

    <script>
        let socket = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('log');
            const statusDiv = document.getElementById('status');
            
            const logMessage = `[${timestamp}] ${message}`;
            logDiv.innerHTML += logMessage + '<br>';
            logDiv.scrollTop = logDiv.scrollHeight;
            
            // Update status
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            
            console.log(logMessage);
        }
        
        function testConnection() {
            log('Testing WebSocket connection...', 'info');
            
            if (socket) {
                socket.close();
                socket = null;
            }
            
            try {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const hostname = window.location.hostname;
                const wsUrl = `${protocol}//${hostname}:8765`;
                
                log(`Attempting to connect to: ${wsUrl}`, 'info');
                
                socket = new WebSocket(wsUrl);
                
                socket.onopen = () => {
                    log('✅ WebSocket connection successful!', 'success');
                    
                    // Test sending a message
                    const testMessage = {
                        type: 'register',
                        name: 'Test User'
                    };
                    
                    socket.send(JSON.stringify(testMessage));
                    log(`Sent test message: ${JSON.stringify(testMessage)}`, 'info');
                };
                
                socket.onmessage = (event) => {
                    log(`📨 Received message: ${event.data}`, 'success');
                };
                
                socket.onclose = (event) => {
                    log(`❌ Connection closed. Code: ${event.code}, Reason: ${event.reason}`, 'error');
                };
                
                socket.onerror = (error) => {
                    log(`❌ WebSocket error: ${error}`, 'error');
                };
                
                // Timeout test
                setTimeout(() => {
                    if (socket && socket.readyState === WebSocket.CONNECTING) {
                        log('⚠️ Connection timeout - still connecting after 5 seconds', 'warning');
                    }
                }, 5000);
                
            } catch (error) {
                log(`❌ Failed to create WebSocket: ${error.message}`, 'error');
            }
        }
        
        function testDisplayName() {
            log('Testing display name prompt...', 'info');
            
            // Clear stored name to force prompt
            localStorage.removeItem('userName');
            
            let userName = localStorage.getItem('userName');
            if (!userName || userName === 'Anonymous') {
                userName = prompt('Welcome! Please enter your display name:') || 'Anonymous';
                localStorage.setItem('userName', userName);
            }
            
            log(`Display name set to: ${userName}`, 'success');
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('Log cleared', 'info');
        }
        
        // Test browser capabilities
        function checkBrowserSupport() {
            log('Checking browser support...', 'info');
            
            const features = {
                'WebSocket': !!window.WebSocket,
                'localStorage': !!window.localStorage,
                'getUserMedia': !!navigator.mediaDevices?.getUserMedia,
                'getDisplayMedia': !!navigator.mediaDevices?.getDisplayMedia,
                'RTCPeerConnection': !!window.RTCPeerConnection
            };
            
            for (const [feature, supported] of Object.entries(features)) {
                log(`${feature}: ${supported ? '✅' : '❌'}`, supported ? 'success' : 'error');
            }
        }
        
        // Auto-run browser support check
        window.addEventListener('load', () => {
            checkBrowserSupport();
            log('Ready to test. Click "Test WebSocket Connection" to begin.', 'info');
        });
    </script>
</body>
</html>
