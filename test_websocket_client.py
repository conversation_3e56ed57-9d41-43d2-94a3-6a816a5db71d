#!/usr/bin/env python3
"""
Simple WebSocket client to test the collaboration server
"""

import asyncio
import json
import websockets
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_websocket_connection():
    """Test WebSocket connection to collaboration server"""
    uri = "ws://localhost:8765"
    
    try:
        logger.info(f"Connecting to {uri}")
        
        async with websockets.connect(uri) as websocket:
            logger.info("✅ Connected successfully!")
            
            # Test registration
            register_message = {
                "type": "register",
                "name": "Test Client"
            }
            
            await websocket.send(json.dumps(register_message))
            logger.info(f"📤 Sent: {register_message}")
            
            # Wait for response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                logger.info(f"📨 Received: {response}")
                
                # Test creating a room
                create_room_message = {
                    "type": "create_room",
                    "room_name": "Test Room",
                    "max_users": 10
                }
                
                await websocket.send(json.dumps(create_room_message))
                logger.info(f"📤 Sent: {create_room_message}")
                
                # Wait for response
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                logger.info(f"📨 Received: {response}")
                
                logger.info("✅ All tests passed!")
                
            except asyncio.TimeoutError:
                logger.error("❌ Timeout waiting for server response")
                
    except ConnectionRefusedError:
        logger.error("❌ Connection refused - is the server running?")
    except Exception as e:
        logger.error(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_websocket_connection())
