#!/usr/bin/env python3
"""
Test script to verify canvas state persistence for late joiners
"""
import asyncio
import websockets
import json
import time

async def test_canvas_persistence():
    """Test that canvas state is preserved for late joiners"""
    print("🧪 Testing Canvas State Persistence for Late Joiners")
    print("=" * 60)
    
    # Test scenario:
    # 1. User A connects and draws something
    # 2. User A creates a room (with existing drawings)
    # 3. User B joins the room later
    # 4. User B should see User A's drawings
    
    uri = "ws://localhost:5002/ws"
    
    try:
        # Simulate User A
        print("👤 User A: Connecting...")
        async with websockets.connect(uri) as websocket_a:
            # Register User A
            await websocket_a.send(json.dumps({
                "type": "register",
                "name": "TestUserA"
            }))
            
            response = await websocket_a.recv()
            user_a_data = json.loads(response)
            user_a_id = user_a_data.get('user_id')
            print(f"✅ User A registered: {user_a_id}")
            
            # Simulate User A creating a room with initial canvas state
            print("🎨 User A: Creating room with initial canvas content...")
            initial_canvas_state = {
                "objects": [
                    {
                        "type": "path",
                        "id": f"{user_a_id}_test_path_1",
                        "path": "M 10 10 L 100 100",
                        "stroke": "#000000",
                        "strokeWidth": 2
                    },
                    {
                        "type": "rect",
                        "id": f"{user_a_id}_test_rect_1",
                        "left": 50,
                        "top": 50,
                        "width": 100,
                        "height": 50,
                        "fill": "#ff0000"
                    }
                ],
                "background": "#ffffff"
            }
            
            await websocket_a.send(json.dumps({
                "type": "create_room",
                "room_name": "Test Room",
                "max_users": 10,
                "initial_canvas_state": initial_canvas_state
            }))
            
            # Wait for room creation response
            response = await websocket_a.recv()
            room_data = json.loads(response)
            
            if room_data.get('type') == 'room_created':
                room_id = room_data.get('room_id')
                print(f"✅ Room created: {room_id}")
                
                # Wait for canvas state response
                response = await websocket_a.recv()
                canvas_state_data = json.loads(response)
                
                if canvas_state_data.get('type') == 'canvas_state':
                    objects_count = len(canvas_state_data.get('state', {}).get('objects', []))
                    print(f"✅ Canvas state confirmed: {objects_count} objects")
                    
                    # Now simulate User B joining
                    print("\n👤 User B: Connecting...")
                    async with websockets.connect(uri) as websocket_b:
                        # Register User B
                        await websocket_b.send(json.dumps({
                            "type": "register",
                            "name": "TestUserB"
                        }))
                        
                        response = await websocket_b.recv()
                        user_b_data = json.loads(response)
                        user_b_id = user_b_data.get('user_id')
                        print(f"✅ User B registered: {user_b_id}")
                        
                        # User B joins the room
                        print(f"🚪 User B: Joining room {room_id}...")
                        await websocket_b.send(json.dumps({
                            "type": "join_room",
                            "room_id": room_id
                        }))
                        
                        # Wait for join confirmation
                        response = await websocket_b.recv()
                        join_data = json.loads(response)
                        
                        if join_data.get('type') == 'room_joined' and join_data.get('success'):
                            print("✅ User B joined room successfully")
                            
                            # Wait for canvas state
                            response = await websocket_b.recv()
                            canvas_state_data = json.loads(response)
                            
                            if canvas_state_data.get('type') == 'canvas_state':
                                received_objects = canvas_state_data.get('state', {}).get('objects', [])
                                received_count = len(received_objects)
                                
                                print(f"📥 User B received canvas state: {received_count} objects")
                                
                                # Verify the objects match
                                if received_count == 2:
                                    print("✅ SUCCESS: User B received all existing canvas objects!")
                                    print("🎉 Canvas persistence for late joiners is working!")
                                    
                                    # Print details
                                    for i, obj in enumerate(received_objects):
                                        print(f"   Object {i+1}: {obj.get('type')} (ID: {obj.get('id')})")
                                    
                                    return True
                                else:
                                    print(f"❌ FAILED: Expected 2 objects, got {received_count}")
                                    return False
                            else:
                                print("❌ FAILED: User B didn't receive canvas state")
                                return False
                        else:
                            print("❌ FAILED: User B couldn't join room")
                            return False
                else:
                    print("❌ FAILED: User A didn't receive canvas state confirmation")
                    return False
            else:
                print("❌ FAILED: Room creation failed")
                return False
                
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

async def main():
    """Run the test"""
    print("🔧 Starting Canvas Persistence Test...")
    print("Make sure the server is running on localhost:5002")
    print()
    
    # Wait a moment for server to be ready
    await asyncio.sleep(1)
    
    success = await test_canvas_persistence()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 TEST PASSED: Canvas persistence for late joiners works!")
    else:
        print("❌ TEST FAILED: Canvas persistence needs fixing")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
