/* Enhanced Video Conferencing UI Styles */

/* Main video container */
#video-conferencing-container {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Participants grid responsive layout */
#participants-grid {
    display: grid;
    gap: 16px;
    padding: 16px;
    height: 100%;
    overflow: hidden;
}

/* Individual participant container */
.participant-container {
    position: relative;
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.participant-container:hover {
    border-color: rgba(99, 102, 241, 0.5);
    transform: scale(1.02);
}

.participant-container.speaking {
    border-color: #10b981;
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
}

/* Video elements */
.participant-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    background: #1f2937;
}

/* Name labels */
.participant-name {
    position: absolute;
    bottom: 8px;
    left: 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

/* Status indicators */
.status-indicators {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    gap: 4px;
}

.status-indicator {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    transition: all 0.2s ease;
}

.status-indicator.video-on {
    background: rgba(16, 185, 129, 0.9);
}

.status-indicator.video-off {
    background: rgba(239, 68, 68, 0.9);
}

.status-indicator.audio-on {
    background: rgba(16, 185, 129, 0.9);
}

.status-indicator.audio-off {
    background: rgba(239, 68, 68, 0.9);
}

/* Controls bar */
.controls-bar {
    background: linear-gradient(90deg, #111827 0%, #1f2937 50%, #111827 100%);
    padding: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    border-top: 1px solid rgba(75, 85, 99, 0.3);
}

.control-button {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.control-button::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.control-button:hover::before {
    opacity: 1;
}

.control-button.video-on {
    background: rgba(75, 85, 99, 0.8);
    color: white;
}

.control-button.video-off {
    background: rgba(239, 68, 68, 0.9);
    color: white;
}

.control-button.audio-on {
    background: rgba(75, 85, 99, 0.8);
    color: white;
}

.control-button.audio-off {
    background: rgba(239, 68, 68, 0.9);
    color: white;
}

.control-button.screen-share {
    background: rgba(59, 130, 246, 0.9);
    color: white;
}

.control-button.screen-share.active {
    background: rgba(16, 185, 129, 0.9);
}

.control-button.end-call {
    background: rgba(239, 68, 68, 0.9);
    color: white;
}

.control-button:hover {
    transform: scale(1.1);
}

.control-button:active {
    transform: scale(0.95);
}

/* Header styles */
.video-header {
    background: linear-gradient(90deg, #111827 0%, #1f2937 50%, #111827 100%);
    color: white;
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(75, 85, 99, 0.3);
}

.call-info {
    display: flex;
    align-items: center;
    gap: 16px;
}

.call-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
}

.call-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.recording-indicator {
    width: 12px;
    height: 12px;
    background: #ef4444;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.call-duration {
    font-size: 14px;
    color: #d1d5db;
    font-weight: 500;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.participant-count {
    font-size: 14px;
    color: #9ca3af;
}

.header-button {
    width: 36px;
    height: 36px;
    border-radius: 6px;
    border: none;
    background: rgba(75, 85, 99, 0.5);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.header-button:hover {
    background: rgba(75, 85, 99, 0.8);
}

.header-button.close {
    background: rgba(239, 68, 68, 0.5);
}

.header-button.close:hover {
    background: rgba(239, 68, 68, 0.8);
}

/* Minimized call indicator */
.minimized-call-indicator {
    background: linear-gradient(135deg, #111827 0%, #1f2937 100%);
    border: 1px solid rgba(75, 85, 99, 0.3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: all 0.2s ease;
}

.minimized-call-indicator:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

/* Responsive grid layouts */
@media (max-width: 768px) {
    #participants-grid {
        padding: 8px;
        gap: 8px;
    }
    
    .controls-bar {
        padding: 12px;
        gap: 12px;
    }
    
    .control-button {
        width: 44px;
        height: 44px;
    }
    
    .video-header {
        padding: 12px 16px;
    }
}

/* Grid layout variations */
.grid-1 { grid-template-columns: 1fr; }
.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(2, 1fr); grid-template-rows: repeat(2, 1fr); }
.grid-6 { grid-template-columns: repeat(3, 1fr); grid-template-rows: repeat(2, 1fr); }
.grid-9 { grid-template-columns: repeat(3, 1fr); grid-template-rows: repeat(3, 1fr); }

/* Audio-only participant */
.audio-only-participant {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    color: white;
    text-align: center;
    padding: 20px;
}

.audio-only-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 12px;
}

.audio-only-name {
    font-size: 14px;
    font-weight: 500;
    opacity: 0.9;
}

/* Connection quality indicator */
.connection-quality {
    position: absolute;
    top: 8px;
    left: 8px;
    display: flex;
    gap: 2px;
}

.quality-bar {
    width: 3px;
    height: 12px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 1px;
}

.quality-bar.active {
    background: #10b981;
}

.quality-bar.medium {
    background: #f59e0b;
}

.quality-bar.poor {
    background: #ef4444;
}

/* Recording button styles */
#record-call {
    background-color: #374151;
}

#record-call:hover {
    background-color: #4b5563;
}

#record-call.recording {
    background-color: #dc2626;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* Screen share button styles */
#share-screen {
    background-color: #6366f1;
}

#share-screen:hover {
    background-color: #4f46e5;
}

#share-screen.sharing {
    background-color: #059669;
}

/* End call button styles */
#end-call {
    background-color: #dc2626;
}

#end-call:hover {
    background-color: #b91c1c;
}
