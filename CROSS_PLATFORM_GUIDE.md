# 🌍 Cross-Platform Collaborative Whiteboard Guide

## 📁 Complete File List

### 🔧 **Core Application Files:**
- `ngrok_app.py` - Main server application (unified Flask + WebSocket)
- `frontend.html` - Web interface (works on all browsers)
- `.env` - API key configuration file

### 🪟 **Windows Scripts:**
- `start_local.bat` - Localhost only
- `start_local_network.bat` - Local network access
- `start_ngrok.bat` - Global access via ngrok

### 🍎 **Mac/Linux Scripts:**
- `install_mac.sh` - One-time setup for Mac
- `start_local.sh` - Localhost only
- `start_local_network.sh` - Local network access  
- `start_ngrok.sh` - Global access via ngrok

### 📖 **Documentation:**
- `SETUP_GUIDE.md` - General setup instructions
- `MAC_SETUP_GUIDE.md` - Mac-specific detailed guide
- `CROSS_PLATFORM_GUIDE.md` - This file

### 🧪 **Testing:**
- `test_image_api.py` - Test script for AI image functionality

---

## 🚀 Quick Start Instructions

### For Your Teacher Demo on Mac:

#### **Option 1: Local Network Demo (Recommended)**
```bash
# One-time setup
./install_mac.sh

# For each demo
./start_local_network.sh
```

**Result:** Students can join from their phones/laptops using your Mac's IP address

#### **Option 2: Global Demo**
```bash
# One-time setup
./install_mac.sh
brew install ngrok/ngrok/ngrok

# For each demo
./start_ngrok.sh
```

**Result:** Anyone worldwide can join using the ngrok URL

---

## 🎯 Demo Scenarios

### **Classroom Demo:**
1. **Your MacBook** runs `./start_local_network.sh`
2. **Students** connect to `http://[YOUR_IP]:5002`
3. **Everyone collaborates** in real-time
4. **AI analyzes** drawings and solves math problems

### **Remote Demo:**
1. **Your MacBook** runs `./start_ngrok.sh`
2. **Share ngrok URL** (e.g., https://abc123.ngrok-free.app)
3. **Anyone globally** can join and collaborate

---

## 🔄 Development Workflow

### **Windows (Development):**
```batch
# Local testing
start_local.bat

# Network testing
start_local_network.bat

# Global sharing
start_ngrok.bat
```

### **Mac (Demo/Presentation):**
```bash
# Setup once
./install_mac.sh

# Local testing
./start_local.sh

# Network demo
./start_local_network.sh

# Global demo
./start_ngrok.sh
```

---

## 📱 Device Compatibility

| Device Type | Can Run Server | Can Join as Client |
|-------------|----------------|-------------------|
| Windows PC | ✅ Yes (.bat) | ✅ Yes (browser) |
| MacBook | ✅ Yes (.sh) | ✅ Yes (browser) |
| Linux PC | ✅ Yes (.sh) | ✅ Yes (browser) |
| iPhone | ❌ No | ✅ Yes (Safari) |
| iPad | ❌ No | ✅ Yes (Safari) |
| Android | ❌ No | ✅ Yes (Chrome) |
| Chromebook | ❌ No | ✅ Yes (Chrome) |

---

## 🛠️ Troubleshooting by Platform

### **Windows Issues:**
```batch
# Python not found
# Install from python.org and check "Add to PATH"

# Modules not found
pip install flask flask-socketio websockets requests python-dotenv Pillow

# Port in use
netstat -ano | findstr :5002
taskkill /PID [PID_NUMBER] /F
```

### **Mac Issues:**
```bash
# Permission denied
chmod +x *.sh

# Python not found
brew install python3

# Modules not found
pip3 install flask flask-socketio websockets requests python-dotenv Pillow

# Port in use
lsof -i :5002
kill -9 [PID]
```

---

## 🎉 Features Available on All Platforms

✅ **Real-time Collaboration** - Multiple users drawing simultaneously  
✅ **Cross-Platform** - Windows, Mac, Linux, mobile devices  
✅ **AI Assistant** - Analyze drawings and solve math problems  
✅ **Video Conferencing** - Google Meet style video calls  
✅ **Canvas Synchronization** - Late joiners see existing content  
✅ **Global Access** - Share with anyone worldwide via ngrok  
✅ **Local Network** - Perfect for classroom/office demos  

---

## 📞 Support Commands

### **Check if server is running:**
```bash
# Windows
netstat -ano | findstr :5002

# Mac/Linux
lsof -i :5002
```

### **Stop stuck server:**
```bash
# Windows
taskkill /f /im python.exe

# Mac/Linux
pkill -f "python3 ngrok_app.py"
```

### **Test API functionality:**
```bash
python3 test_image_api.py
```

---

## 🎓 Perfect for Education

This setup is ideal for:
- **Classroom demonstrations**
- **Remote learning**
- **Collaborative problem solving**
- **Math tutoring with AI**
- **Cross-platform compatibility**

Your students can join from any device and collaborate in real-time! 🚀
