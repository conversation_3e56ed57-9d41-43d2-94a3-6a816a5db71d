#!/usr/bin/env python3
"""
Test script to verify image API functionality
"""
import requests
import base64
import json
from PIL import Image, ImageDraw, ImageFont
import io

def create_test_image():
    """Create a simple test image with math equation"""
    # Create a white image
    img = Image.new('RGB', (400, 200), color='white')
    draw = ImageDraw.Draw(img)
    
    # Draw "2 + 2 = ?" in black
    try:
        # Try to use a larger font
        font = ImageFont.truetype("arial.ttf", 40)
    except:
        # Fallback to default font
        font = ImageFont.load_default()
    
    text = "2 + 2 = ?"
    draw.text((50, 80), text, fill='black', font=font)
    
    return img

def image_to_base64(image):
    """Convert PIL image to base64 string"""
    buffer = io.BytesIO()
    image.save(buffer, format='PNG')
    img_str = base64.b64encode(buffer.getvalue()).decode()
    return img_str

def test_image_api():
    """Test the image API endpoint"""
    print("🧪 Testing Image API...")
    
    # Create test image
    print("📸 Creating test image...")
    test_img = create_test_image()
    img_base64 = image_to_base64(test_img)
    
    print(f"✅ Test image created, base64 length: {len(img_base64)}")
    
    # Prepare request
    url = "http://localhost:5002/api/chat"
    payload = {
        "message": "solve this math problem",
        "image_data": img_base64
    }
    
    print("🌐 Sending request to API...")
    print(f"📝 Message: {payload['message']}")
    print(f"🖼️ Image data length: {len(payload['image_data'])}")
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        
        print(f"📡 Response status: {response.status_code}")
        
        if response.ok:
            data = response.json()
            print("✅ SUCCESS!")
            print(f"🤖 AI Response: {data.get('response', 'No response')}")
        else:
            print("❌ FAILED!")
            print(f"Error: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    test_image_api()
