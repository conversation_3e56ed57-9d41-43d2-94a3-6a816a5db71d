// Comprehensive test suite for video conferencing system
class VideoConferencingTests {
    constructor() {
        this.testResults = [];
        this.videoConferencing = null;
        this.mockSocket = null;
    }

    // Create mock WebSocket
    createMockSocket() {
        return {
            send: (data) => {
                console.log('Mock socket sending:', JSON.parse(data));
                this.log('WebSocket message sent: ' + data);
            },
            readyState: 1, // OPEN
            addEventListener: () => {},
            removeEventListener: () => {}
        };
    }

    log(message) {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}] ${message}`);
        this.testResults.push(`[${timestamp}] ${message}`);
    }

    async runAllTests() {
        this.log('Starting comprehensive video conferencing tests...');
        
        try {
            await this.testBrowserSupport();
            await this.testVideoConferencingInitialization();
            await this.testMediaPermissions();
            await this.testVideoCallFlow();
            await this.testScreenSharing();
            await this.testRecording();
            await this.testErrorHandling();
            await this.testPerformance();
            
            this.log('All tests completed!');
            this.generateReport();
            
        } catch (error) {
            this.log('Test suite failed: ' + error.message);
            console.error('Test error:', error);
        }
    }

    async testBrowserSupport() {
        this.log('Testing browser support...');
        
        const features = {
            'getUserMedia': !!navigator.mediaDevices?.getUserMedia,
            'getDisplayMedia': !!navigator.mediaDevices?.getDisplayMedia,
            'RTCPeerConnection': !!window.RTCPeerConnection,
            'MediaRecorder': !!window.MediaRecorder,
            'WebSocket': !!window.WebSocket
        };
        
        for (const [feature, supported] of Object.entries(features)) {
            this.log(`${feature}: ${supported ? 'PASS' : 'FAIL'}`);
        }
    }

    async testVideoConferencingInitialization() {
        this.log('Testing VideoConferencing initialization...');
        
        try {
            this.videoConferencing = new VideoConferencing();
            this.mockSocket = this.createMockSocket();
            
            this.videoConferencing.initialize(
                this.mockSocket, 
                'test-room-123', 
                'test-user-123', 
                'Test User'
            );
            
            this.log('VideoConferencing initialization: PASS');
            
        } catch (error) {
            this.log('VideoConferencing initialization: FAIL - ' + error.message);
            throw error;
        }
    }

    async testMediaPermissions() {
        this.log('Testing media permissions...');
        
        try {
            // Test getUserMedia
            const stream = await navigator.mediaDevices.getUserMedia({
                video: true,
                audio: true
            });
            
            this.log('getUserMedia: PASS');
            
            // Clean up
            stream.getTracks().forEach(track => track.stop());
            
        } catch (error) {
            this.log('getUserMedia: FAIL - ' + error.message);
        }
        
        try {
            // Test getDisplayMedia (may fail if user denies)
            const screenStream = await navigator.mediaDevices.getDisplayMedia({
                video: true
            });
            
            this.log('getDisplayMedia: PASS');
            
            // Clean up
            screenStream.getTracks().forEach(track => track.stop());
            
        } catch (error) {
            this.log('getDisplayMedia: FAIL - ' + error.message);
        }
    }

    async testVideoCallFlow() {
        this.log('Testing video call flow...');
        
        try {
            // Set up callbacks
            this.videoConferencing.onCallStarted = () => this.log('Call started callback triggered');
            this.videoConferencing.onCallEnded = () => this.log('Call ended callback triggered');
            this.videoConferencing.onError = (error) => this.log('Error callback: ' + error);
            
            // Start call
            await this.videoConferencing.startCall();
            this.log('Start call: PASS');
            
            // Wait a moment
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Test media controls
            this.videoConferencing.toggleVideo();
            this.log('Toggle video: PASS');
            
            this.videoConferencing.toggleAudio();
            this.log('Toggle audio: PASS');
            
            // End call
            this.videoConferencing.endCall();
            this.log('End call: PASS');
            
        } catch (error) {
            this.log('Video call flow: FAIL - ' + error.message);
        }
    }

    async testScreenSharing() {
        this.log('Testing screen sharing...');
        
        try {
            if (this.videoConferencing.isCallActive) {
                await this.videoConferencing.toggleScreenShare();
                this.log('Screen sharing toggle: PASS');
            } else {
                this.log('Screen sharing: SKIP (no active call)');
            }
            
        } catch (error) {
            this.log('Screen sharing: FAIL - ' + error.message);
        }
    }

    async testRecording() {
        this.log('Testing recording functionality...');
        
        try {
            if (this.videoConferencing.localStream) {
                this.videoConferencing.startRecording();
                this.log('Start recording: PASS');
                
                // Wait a moment
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                this.videoConferencing.stopRecording();
                this.log('Stop recording: PASS');
            } else {
                this.log('Recording: SKIP (no local stream)');
            }
            
        } catch (error) {
            this.log('Recording: FAIL - ' + error.message);
        }
    }

    async testErrorHandling() {
        this.log('Testing error handling...');
        
        try {
            // Test with invalid parameters
            const invalidVC = new VideoConferencing();
            invalidVC.initialize(null, '', '', '');
            
            this.log('Error handling: PASS');
            
        } catch (error) {
            this.log('Error handling: PASS (expected error caught)');
        }
    }

    async testPerformance() {
        this.log('Testing performance...');
        
        const startTime = performance.now();
        
        try {
            // Create multiple peer connections to simulate load
            const connections = [];
            for (let i = 0; i < 5; i++) {
                const pc = new RTCPeerConnection({
                    iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
                });
                connections.push(pc);
            }
            
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            this.log(`Performance test: ${duration.toFixed(2)}ms for 5 peer connections`);
            
            // Clean up
            connections.forEach(pc => pc.close());
            
        } catch (error) {
            this.log('Performance test: FAIL - ' + error.message);
        }
    }

    generateReport() {
        this.log('=== TEST REPORT ===');
        
        const passCount = this.testResults.filter(r => r.includes('PASS')).length;
        const failCount = this.testResults.filter(r => r.includes('FAIL')).length;
        const skipCount = this.testResults.filter(r => r.includes('SKIP')).length;
        
        this.log(`Total tests: ${passCount + failCount + skipCount}`);
        this.log(`Passed: ${passCount}`);
        this.log(`Failed: ${failCount}`);
        this.log(`Skipped: ${skipCount}`);
        
        if (failCount === 0) {
            this.log('🎉 All tests passed!');
        } else {
            this.log('⚠️ Some tests failed. Check the logs above.');
        }
    }
}

// Export for use in test page
window.VideoConferencingTests = VideoConferencingTests;

// Auto-run tests if this script is loaded directly
if (typeof window !== 'undefined' && window.location.pathname.includes('test')) {
    window.addEventListener('load', async () => {
        const tests = new VideoConferencingTests();
        await tests.runAllTests();
    });
}
